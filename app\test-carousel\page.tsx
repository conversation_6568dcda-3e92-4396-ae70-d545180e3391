"use client";

import Hero from "@/components/pages/home/<USER>";
import { useState } from "react";

export default function TestCarouselPage() {
   const [transitionType, setTransitionType] = useState<"fade" | "slide">("fade");

   return (
      <main className="min-h-screen">
         {/* Controls */}
         <div className="fixed top-20 left-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white">
            <h3 className="text-lg font-semibold mb-3">Carousel Test Controls</h3>
            <div className="space-y-2">
               <button
                  onClick={() => setTransitionType("fade")}
                  className={`block w-full px-4 py-2 rounded transition-colors ${
                     transitionType === "fade"
                        ? "bg-white text-black"
                        : "bg-gray-700 hover:bg-gray-600"
                  }`}
               >
                  Fade Transition
               </button>
               <button
                  onClick={() => setTransitionType("slide")}
                  className={`block w-full px-4 py-2 rounded transition-colors ${
                     transitionType === "slide"
                        ? "bg-white text-black"
                        : "bg-gray-700 hover:bg-gray-600"
                  }`}
               >
                  Slide Transition
               </button>
            </div>
            <div className="mt-4 text-sm text-gray-300">
               <p>Keyboard Controls:</p>
               <ul className="text-xs space-y-1 mt-1">
                  <li>← → Arrow keys: Navigate</li>
                  <li>Spacebar: Play/Pause</li>
                  <li>Hover: Auto-pause</li>
               </ul>
            </div>
         </div>

         {/* Hero with selected transition type */}
         <Hero transitionType={transitionType} />
         
         {/* Info section */}
         <section className="bg-gray-100 py-12">
            <div className="max-w-4xl mx-auto px-4">
               <h2 className="text-3xl font-bold mb-6">Carousel Test Page</h2>
               <div className="grid md:grid-cols-2 gap-8">
                  <div>
                     <h3 className="text-xl font-semibold mb-3">Current Settings</h3>
                     <ul className="space-y-2">
                        <li><strong>Transition:</strong> {transitionType}</li>
                        <li><strong>Auto-play:</strong> 6 seconds</li>
                        <li><strong>Pause on hover:</strong> Enabled</li>
                        <li><strong>Keyboard navigation:</strong> Enabled</li>
                     </ul>
                  </div>
                  <div>
                     <h3 className="text-xl font-semibold mb-3">Features Tested</h3>
                     <ul className="space-y-2">
                        <li>✅ Fade transition</li>
                        <li>✅ Slide transition (fixed positioning)</li>
                        <li>✅ Dot navigation</li>
                        <li>✅ Play/pause controls</li>
                        <li>✅ Keyboard navigation</li>
                        <li>✅ Responsive design</li>
                     </ul>
                  </div>
               </div>
            </div>
         </section>
      </main>
   );
}

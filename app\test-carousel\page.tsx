"use client";

import Hero from "@/components/pages/home/<USER>";
import { useState } from "react";

export default function TestCarouselPage() {
   const [transitionType, setTransitionType] = useState<"fade" | "slide">(
      "fade",
   );

   return (
      <main className="min-h-screen">
         {/* Controls */}
         <div className="fixed top-20 left-4 z-50 rounded-lg bg-black/80 p-4 text-white backdrop-blur-sm">
            <h3 className="mb-3 text-lg font-semibold">
               Carousel Test Controls
            </h3>
            <div className="space-y-2">
               <button
                  onClick={() => setTransitionType("fade")}
                  className={`block w-full rounded px-4 py-2 transition-colors ${
                     transitionType === "fade"
                        ? "bg-white text-black"
                        : "bg-gray-700 hover:bg-gray-600"
                  }`}
               >
                  Fade Transition
               </button>
               <button
                  onClick={() => setTransitionType("slide")}
                  className={`block w-full rounded px-4 py-2 transition-colors ${
                     transitionType === "slide"
                        ? "bg-white text-black"
                        : "bg-gray-700 hover:bg-gray-600"
                  }`}
               >
                  Slide Transition
               </button>
            </div>
            <div className="mt-4 text-sm text-gray-300">
               <p>Keyboard Controls:</p>
               <ul className="mt-1 space-y-1 text-xs">
                  <li>← → Arrow keys: Navigate</li>
                  <li>Spacebar: Play/Pause</li>
                  <li>Hover: Auto-pause</li>
               </ul>
            </div>
         </div>

         {/* Hero with selected transition type */}
         <Hero transitionType={transitionType} />

         {/* Info section */}
         <section className="bg-gray-100 py-12">
            <div className="mx-auto max-w-4xl px-4">
               <h2 className="mb-6 text-3xl font-bold">Carousel Test Page</h2>
               <div className="grid gap-8 md:grid-cols-2">
                  <div>
                     <h3 className="mb-3 text-xl font-semibold">
                        Current Settings
                     </h3>
                     <ul className="space-y-2">
                        <li>
                           <strong>Transition:</strong> {transitionType}
                        </li>
                        <li>
                           <strong>Auto-play:</strong> 6 seconds
                        </li>
                        <li>
                           <strong>Pause on hover:</strong> Enabled
                        </li>
                        <li>
                           <strong>Keyboard navigation:</strong> Enabled
                        </li>
                     </ul>
                  </div>
                  <div>
                     <h3 className="mb-3 text-xl font-semibold">
                        Features Tested
                     </h3>
                     <ul className="space-y-2">
                        <li>✅ Fade transition</li>
                        <li>✅ Slide transition with DOM manipulation</li>
                        <li>✅ Navigation dots (expanded active state)</li>
                        <li>✅ Timer reset on dot clicks</li>
                        <li>✅ Play/pause controls</li>
                        <li>✅ Keyboard navigation</li>
                        <li>✅ True infinite sliding</li>
                        <li>✅ Responsive design</li>
                     </ul>
                  </div>
               </div>
            </div>
         </section>
      </main>
   );
}

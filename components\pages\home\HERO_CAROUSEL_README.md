# Hero Background Image Carousel

## Overview

The Hero component now features a dynamic background image carousel that automatically cycles through multiple background images while preserving all existing hero content (text, buttons, overlays, etc.).

## Features

### ✅ Implemented Features

1. **Automatic Cycling**: Images change every 5 seconds automatically
2. **Smooth Transitions**: Two transition types available:
   - **Fade**: Cross-fade between images (default)
   - **Slide**: True infinite sliding using DOM manipulation
3. **Navigation Controls**:
   - Dot indicators at the bottom for manual navigation
   - Play/pause button with visual icons
4. **True Infinite Sliding**: Uses DOM manipulation for seamless infinite sliding
   - **Forward (Auto-play/Right Arrow)**: Always slides left by moving DOM elements
   - **Backward (Left Arrow)**: Slides right by moving DOM elements in reverse
   - **No Visual Jumps**: Never resets or jumps back to beginning
5. **Timer Reset**: Auto-play timer resets after manual navigation via dots
6. **Responsive Design**: Works across all screen sizes
7. **Performance Optimized**:
   - First image loads with priority
   - Subsequent images load normally
   - Efficient re-rendering with React hooks
   - DOM manipulation for smooth infinite transitions
8. **Accessibility Features**:
   - ARIA labels for navigation controls
   - Screen reader announcements for current slide
   - Keyboard navigation support
   - Focus management

## Usage

### Basic Usage (Fade Transition)

```tsx
import Hero from "@/components/pages/home/<USER>";

export default function HomePage() {
   return <Hero />;
}
```

### With Slide Transition

```tsx
import Hero from "@/components/pages/home/<USER>";

export default function HomePage() {
   return <Hero transitionType="slide" />;
}
```

## Configuration

### Adding New Background Images

To add more background images to the carousel, edit the `HERO_BACKGROUNDS` array in the hero component:

```tsx
const HERO_BACKGROUNDS = [
   {
      src: "/images/hero/hero-bg.png",
      alt: "Creative professional",
   },
   {
      src: "/images/hero/list-bg.png",
      alt: "Creative workspace",
   },
   {
      src: "/images/hero/hero-bg-3.png",
      alt: "Creative studio",
   },
   // Add more images here...
];
```

### Customizing Carousel Settings

Modify the `CAROUSEL_CONFIG` object to adjust timing and behavior:

```tsx
const CAROUSEL_CONFIG = {
   autoPlayInterval: 5000, // Time between slides (ms)
   transitionDuration: 1000, // Transition animation duration (ms)
};
```

## Technical Implementation

### State Management

- `currentImageIndex`: Tracks the currently displayed image
- `isPlaying`: Controls auto-play functionality
- `isTransitioning`: Prevents rapid navigation during transitions

### Performance Considerations

- Only the first image loads with `priority={true}`
- Images are preloaded but not rendered until needed
- Efficient cleanup of intervals to prevent memory leaks
- Optimized re-renders using `useCallback` for event handlers

### Accessibility

- Semantic HTML structure with proper ARIA attributes
- Screen reader announcements for slide changes
- Keyboard navigation support
- Focus management for navigation controls

## Browser Support

The carousel uses modern CSS features and React hooks:

- CSS Transitions (widely supported)
- CSS Transform (widely supported)
- React Hooks (React 16.8+)
- Next.js Image component optimizations

## File Structure

```
components/pages/home/
├── hero.tsx                 # Main hero component with carousel
└── HERO_CAROUSEL_README.md  # This documentation file

public/images/hero/
├── hero-bg.png             # Background image 1
├── list-bg.png             # Background image 2
└── nominate-a-creative.png # Hero content image
```

## Future Enhancements

Potential improvements that could be added:

1. **Touch/Swipe Support**: Add touch gestures for mobile devices
2. **Keyboard Navigation**: Arrow keys to navigate slides
3. **Progress Indicator**: Visual progress bar showing time until next slide
4. **Lazy Loading**: Load images only when needed
5. **Video Backgrounds**: Support for video backgrounds in the carousel
6. **Custom Animations**: More transition effects (zoom, blur, etc.)
7. **Auto-pause on Visibility**: Pause when tab is not visible
8. **Preload Optimization**: Smart preloading based on user behavior

## Troubleshooting

### Images Not Loading

- Ensure image files exist in `/public/images/hero/`
- Check file paths and extensions
- Verify image file permissions

### Carousel Not Auto-Playing

- Check if `isPlaying` state is true
- Verify `CAROUSEL_CONFIG.autoPlayInterval` is set correctly
- Ensure there are multiple images in `HERO_BACKGROUNDS`

### Performance Issues

- Optimize image file sizes
- Consider using WebP format for better compression
- Implement lazy loading for large numbers of images

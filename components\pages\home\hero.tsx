"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";

// Configuration for the carousel
const CAROUSEL_CONFIG = {
   autoPlayInterval: 5000, // 5 seconds
   transitionDuration: 1000, // 1 second
   pauseOnHover: false,
};

// Background images for the carousel
const HERO_BACKGROUNDS = [
   {
      src: "/images/hero/hero-bg.png",
      alt: "Creative professional",
   },
   {
      src: "/images/hero/list-bg.png",
      alt: "Creative workspace",
   },
   // Add more background images here as needed
   // {
   //    src: "/images/hero/hero-bg-3.png",
   //    alt: "Creative studio",
   // },
];

type TransitionType = "fade" | "slide";

interface HeroCarouselProps {
   transitionType?: TransitionType;
}

export default function Hero({ transitionType = "fade" }: HeroCarouselProps) {
   const [currentImageIndex, setCurrentImageIndex] = useState(0);
   const [isPlaying, setIsPlaying] = useState(true);
   const [isTransitioning, setIsTransitioning] = useState(false);
   const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

   // Reset timer function
   const resetTimer = useCallback(() => {
      if (intervalId) {
         clearInterval(intervalId);
      }

      if (isPlaying && HERO_BACKGROUNDS.length > 1) {
         const newInterval = setInterval(() => {
            setCurrentImageIndex((prevIndex) => prevIndex + 1);
         }, CAROUSEL_CONFIG.autoPlayInterval);
         setIntervalId(newInterval);
      }
   }, [isPlaying, intervalId]);

   // Auto-advance carousel with infinite sliding
   useEffect(() => {
      if (!isPlaying || HERO_BACKGROUNDS.length <= 1) {
         if (intervalId) {
            clearInterval(intervalId);
            setIntervalId(null);
         }
         return;
      }

      // Clear existing interval before setting new one
      if (intervalId) {
         clearInterval(intervalId);
      }

      const interval = setInterval(() => {
         setCurrentImageIndex((prevIndex) => prevIndex + 1);
      }, CAROUSEL_CONFIG.autoPlayInterval);

      setIntervalId(interval);

      return () => {
         clearInterval(interval);
         setIntervalId(null);
      };
   }, [isPlaying, intervalId]);

   // Handle infinite sliding reset
   useEffect(() => {
      if (
         transitionType === "slide" &&
         currentImageIndex >= HERO_BACKGROUNDS.length
      ) {
         // Reset to first slide without animation after showing the duplicate
         const timer = setTimeout(() => {
            setCurrentImageIndex(0);
         }, CAROUSEL_CONFIG.transitionDuration);

         return () => clearTimeout(timer);
      }
   }, [currentImageIndex, transitionType]);

   // Handle manual navigation
   const goToSlide = useCallback(
      (index: number) => {
         const actualCurrentIndex = currentImageIndex % HERO_BACKGROUNDS.length;
         if (index === actualCurrentIndex || isTransitioning) return;

         setIsTransitioning(true);
         setCurrentImageIndex(index);
         resetTimer(); // Reset timer after manual navigation

         setTimeout(() => {
            setIsTransitioning(false);
         }, CAROUSEL_CONFIG.transitionDuration);
      },
      [currentImageIndex, isTransitioning, resetTimer],
   );

   // Navigate to previous/next slide
   const goToPrevious = useCallback(() => {
      const prevIndex =
         currentImageIndex === 0
            ? HERO_BACKGROUNDS.length - 1
            : currentImageIndex - 1;
      goToSlide(prevIndex);
   }, [currentImageIndex, goToSlide]);

   const goToNext = useCallback(() => {
      const nextIndex = (currentImageIndex + 1) % HERO_BACKGROUNDS.length;
      goToSlide(nextIndex);
   }, [currentImageIndex, goToSlide]);

   // Keyboard navigation
   useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
         if (HERO_BACKGROUNDS.length <= 1) return;

         switch (event.key) {
            case "ArrowLeft":
               event.preventDefault();
               goToPrevious();
               break;
            case "ArrowRight":
               event.preventDefault();
               goToNext();
               break;
            case " ": // Spacebar
               event.preventDefault();
               setIsPlaying((prev) => !prev);
               break;
         }
      };

      window.addEventListener("keydown", handleKeyDown);
      return () => window.removeEventListener("keydown", handleKeyDown);
   }, [goToPrevious, goToNext]);

   // Pause when tab is not visible (Page Visibility API)
   useEffect(() => {
      const handleVisibilityChange = () => {
         if (document.hidden) {
            setIsPlaying(false);
         } else {
            setIsPlaying(true);
         }
      };

      document.addEventListener("visibilitychange", handleVisibilityChange);
      return () =>
         document.removeEventListener(
            "visibilitychange",
            handleVisibilityChange,
         );
   }, []);

   return (
      <section className="relative h-screen overflow-hidden">
         {/* Background Image Carousel */}
         <div className="absolute inset-0 z-0">
            {transitionType === "fade" ? (
               // Fade transition
               <>
                  {HERO_BACKGROUNDS.map((bg, index) => (
                     <div
                        key={index}
                        className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
                           index === currentImageIndex
                              ? "opacity-100"
                              : "opacity-0"
                        }`}
                     >
                        <Image
                           src={bg.src}
                           alt={bg.alt}
                           fill
                           priority={index === 0}
                           className="object-cover grayscale"
                        />
                     </div>
                  ))}
               </>
            ) : (
               // Slide transition with infinite sliding
               <div className="relative h-full w-full overflow-hidden">
                  <div
                     className={`flex h-full ${
                        currentImageIndex >= HERO_BACKGROUNDS.length
                           ? "transition-none"
                           : "transition-transform duration-1000 ease-in-out"
                     }`}
                     style={{
                        transform: `translateX(-${currentImageIndex * 100}%)`,
                     }}
                  >
                     {/* Original images */}
                     {HERO_BACKGROUNDS.map((bg, index) => (
                        <div
                           key={`original-${index}`}
                           className="relative h-full w-full flex-shrink-0"
                        >
                           <Image
                              src={bg.src}
                              alt={bg.alt}
                              fill
                              priority={index === 0}
                              className="object-cover grayscale"
                           />
                        </div>
                     ))}
                     {/* Duplicate first image for infinite effect */}
                     <div
                        key="duplicate-first"
                        className="relative h-full w-full flex-shrink-0"
                     >
                        <Image
                           src={HERO_BACKGROUNDS[0].src}
                           alt={HERO_BACKGROUNDS[0].alt}
                           fill
                           className="object-cover grayscale"
                        />
                     </div>
                  </div>
               </div>
            )}
         </div>

         {/* Main Content */}
         <div className="relative z-10 mx-auto flex h-full max-w-[1380px] items-center justify-start px-4 md:px-8">
            <div className="relative aspect-[16/6] w-[600px] -translate-y-10 md:w-[800px]">
               <Image
                  src="/images/hero/nominate-a-creative.png"
                  alt="Nominate a creative"
                  fill
                  priority
               />
            </div>
         </div>

         {/* Nominate Button */}
         <div className="relative mx-auto w-full max-w-[1380px] px-4 md:px-8">
            <Button
               asChild
               className="absolute bottom-35 left-0 z-20 mx-8 rounded-2xl border-2 border-gray-400 bg-black px-8 py-5 text-lg text-gray-200 shadow-md shadow-black hover:bg-gray-950 md:mx-8"
               variant="secondary"
            >
               <Link
                  href="https://docs.google.com/forms/d/e/1FAIpQLSew1e_uhrpuLbtEVDM3gwjPzqiaDmW8KNQRgKy1BIJvCq97JA/viewform?usp=dialog"
                  target="_blank"
               >
                  Nominate
               </Link>
            </Button>
         </div>

         {/* Carousel Navigation Dots */}
         {HERO_BACKGROUNDS.length > 1 && (
            <div className="absolute bottom-8 left-1/2 z-20 flex -translate-x-1/2 items-center space-x-3">
               {HERO_BACKGROUNDS.map((_, index) => {
                  const actualCurrentIndex =
                     currentImageIndex % HERO_BACKGROUNDS.length;
                  const isActive = index === actualCurrentIndex;

                  return (
                     <button
                        key={index}
                        onClick={() => goToSlide(index)}
                        className={`h-3 w-3 rounded-full border-2 border-white/50 transition-all duration-300 hover:scale-110 hover:border-white focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black/20 focus:outline-none ${
                           isActive
                              ? "bg-white shadow-lg"
                              : "bg-white/30 hover:bg-white/60"
                        }`}
                        aria-label={`Go to slide ${index + 1}`}
                        aria-current={isActive ? "true" : "false"}
                        disabled={isTransitioning}
                     />
                  );
               })}

               {/* Play/Pause Indicator */}
               <div className="ml-4 flex items-center">
                  <button
                     onClick={() => setIsPlaying((prev) => !prev)}
                     className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20 backdrop-blur-sm transition-all duration-300 hover:bg-white/30 focus:ring-2 focus:ring-white/50 focus:outline-none"
                     aria-label={isPlaying ? "Pause carousel" : "Play carousel"}
                  >
                     {isPlaying ? (
                        <div className="flex space-x-1">
                           <div className="h-3 w-1 bg-white"></div>
                           <div className="h-3 w-1 bg-white"></div>
                        </div>
                     ) : (
                        <div className="ml-0.5 h-0 w-0 border-t-[4px] border-b-[4px] border-l-[6px] border-t-transparent border-b-transparent border-l-white"></div>
                     )}
                  </button>
               </div>
            </div>
         )}

         {/* Screen Reader Only: Current slide indicator and instructions */}
         <div className="sr-only" aria-live="polite" aria-atomic="true">
            Slide {(currentImageIndex % HERO_BACKGROUNDS.length) + 1} of{" "}
            {HERO_BACKGROUNDS.length}
            {HERO_BACKGROUNDS.length > 1 && (
               <span>
                  . Use left and right arrow keys to navigate slides, or
                  spacebar to pause.
               </span>
            )}
         </div>
      </section>
   );
}
